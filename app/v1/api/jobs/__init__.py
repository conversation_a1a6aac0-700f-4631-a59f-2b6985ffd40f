from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, UploadFile, File, Form, Query
from fastapi.responses import JSONResponse
from typing import List, Optional
from datetime import datetime, timezone, timedelta
from bson.objectid import ObjectId
from app.core.security import require_roles
import io
from app.models.user import UserTenantDB
import requests
import json

from app.v1.processes.generic_entity_extraction import GenericEntity
from app.v1.schema.pagination import PaginationResponse
from app.core.helper.logger import setup_new_logging
from app.v1.webhook.webhook import trigger_webhook
from app.v1.webhook.models import WebhookCreate, WebhookStatus

from .models import (
    Job, JobCreate, ProcessItem, JobStatus, JobUpdate,
    JobAssignment, JobUnassignment, JobWithAssignmentInfo,
    OutputVerification, OutputEdit, VerificationCount
)

logger = setup_new_logging(__name__)

router = APIRouter(tags=["Jobs"], prefix="/jobs")


@router.post("/create/{project_id}", response_model=Job)
async def create_job(project_id:str, job: JobCreate, user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))):
    jobs_collection = user_tenant_info.async_db.jobs
    projects_collection = user_tenant_info.async_db.projects

    # Verify project exists
    if not ObjectId.is_valid(project_id):
        raise HTTPException(status_code=400, detail="Invalid project ID")

    project = await projects_collection.find_one({"_id": ObjectId(project_id)})
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    new_job = {
        "name": job.name,
        "description": job.description,
        "process_name": job.process_name,
        "created_at": datetime.now(timezone.utc),
        "created_by": ObjectId(user_tenant_info.user.id),
        "project_id": ObjectId(project_id),
        "items": [],
        "status": JobStatus.PENDING,
    }

    result = await jobs_collection.insert_one(new_job)
    new_job["_id"] = result.inserted_id
    return new_job


@router.post("/add-media/{job_id}", response_model=Job)
async def add_media(
    job_id: str,
    media_uris: List[str] = Form(default=[]),
    media_files: List[UploadFile] = File(default=[]),
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    """
    Add media to a job from URIs and/or file uploads.

    - media_uris: List of URIs pointing to media files
    - media_files: List of uploaded files
    """
    jobs_collection = user_tenant_info.async_db.jobs

    # Verify job exists
    if not ObjectId.is_valid(job_id):
        raise HTTPException(status_code=400, detail="Invalid job ID")

    job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Split URLs if they contain commas
    expanded_uris = []
    for uri in media_uris:
        if ',' in uri:
            expanded_uris.extend([u.strip() for u in uri.split(',') if u.strip()])
        else:
            expanded_uris.append(uri.strip())

    # Filter out empty URIs
    media_uris = [uri for uri in expanded_uris if uri]

    # Get process_name and determine media type
    process_name = job["process_name"]

    # Validate media type based on process
    if process_name == "extract-image-data":
        media_type = "image"
        valid_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
    elif process_name == "audio-transcribe-analysis":
        media_type = "audio"
        valid_extensions = ['mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a']
    elif process_name == "generic-entity-extraction":
        media_type = "document"
        valid_extensions = ['json']
    else:
        media_type = ""  # Default type
        valid_extensions = []  # No validation for document type

    # Validate file extensions if needed
    if valid_extensions:
        # Check URIs
        for uri in media_uris:
            base_uri = uri.split('?')[0]
            file_ext = base_uri.split('.')[-1].lower() if '.' in base_uri else ''
            if file_ext and file_ext not in valid_extensions:
                raise HTTPException(status_code=400,
                    detail=f"Process {process_name} requires {media_type} files. Found non-{media_type}: {uri}")

        # Check files
        for file in media_files:
            file_ext = file.filename.split('.')[-1].lower() if '.' in file.filename else ''
            if file_ext and file_ext not in valid_extensions:
                raise HTTPException(status_code=400,
                    detail=f"Process {process_name} requires {media_type} files. Found non-{media_type}: {file.filename}")

    # Process items to be added to the job
    process_items = []

    # Helper function to process a file once we have its bytes and filename
    async def process_file_data(filename, file_bytes, file_size, source_info):
        # If filename doesn't have extension, append appropriate extension
        if media_type == "image" and not any(filename.lower().endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']):
            filename = f"{filename}.png"
            content_type = "image/png"
        elif media_type == "audio" and not any(filename.lower().endswith(ext) for ext in ['.mp3', '.wav', '.ogg', '.flac', '.aac', '.m4a']):
            filename = f"{filename}.mp3"
            content_type = "audio/mpeg"
        elif media_type == "document" and not any(filename.lower().endswith(ext) for ext in ['.json']):
            filename = f"{filename}.json"
            content_type = "application/json"
        else:
            if media_type == "image":
                content_type = "image/png"
            elif media_type == "audio":
                content_type = "audio/mpeg"
            elif media_type == "document":
                content_type = "application/json"
            else:
                content_type = "application/octet-stream"

        object_name = f"{job_id}/input/{filename}"

        try:
            # Upload to MinIO
            user_tenant_info.minio_client.put_object(
                bucket_name=user_tenant_info.minio_bucket_name,
                object_name=object_name,
                data=file_bytes,
                length=file_size,
                content_type=content_type
            )

            # Create Media object
            media_data = {
                "filename": filename,
                "content_type": content_type,
                "bucket_name": user_tenant_info.minio_bucket_name,
                "object_name": object_name,
                "job_id": ObjectId(job_id),
                "created_at": datetime.now(timezone.utc),
                "created_by": ObjectId(user_tenant_info.user.id),
                "uploaded_at": datetime.now(timezone.utc),
                "uploaded_by": ObjectId(user_tenant_info.user.id),
                "size": file_size,
                "metadata": source_info
            }

            # Save media to database
            media_collection = user_tenant_info.async_db.media
            result = await media_collection.insert_one(media_data)
            media_id = result.inserted_id

            return ProcessItem(input_id=ObjectId(media_id), output_id=None, status=JobStatus.PENDING).to_dict()

        except Exception as e:
            logger.error(f"Error uploading file {filename}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to upload file: {str(e)}")

    # Process URIs
    logger.debug(f"\nProcessing URIs: {media_uris}, {type(media_uris)}")
    for uri in media_uris:
        # Get base URL without query parameters for filename
        base_uri = uri.split('?')[0]
        filename = base_uri.split('/')[-1]

        try:
            # Download the file from original URI
            logger.debug(f"\nDownloading file from URI: {uri}")
            response = requests.get(uri)
            logger.debug(f"\nResponse status code: {response}")
            if response.status_code != 200:
                raise HTTPException(status_code=400, detail=f"Failed to download file from URI: {uri}")

            file_bytes = io.BytesIO(response.content)
            file_size = len(response.content)

            # Process the file data
            item = await process_file_data(filename, file_bytes, file_size, {"source_uri": uri})
            process_items.append(item)

        except Exception as e:
            logger.error(f"Error uploading media {uri}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to upload media: {str(e)}")

    # Process uploaded files
    for file in media_files:
        try:
            # Read file content
            file_content = await file.read()
            file_bytes = io.BytesIO(file_content)
            file_size = len(file_content)

            # Process the file data
            item = await process_file_data(file.filename, file_bytes, file_size, {"source_type": "file_upload"})
            process_items.append(item)

            # Reset file cursor for potential reuse
            await file.seek(0)

        except Exception as e:
            logger.error(f"Error uploading file {file.filename}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to upload file: {str(e)}")

    # Update job with new media
    if "items" not in job:
        job["items"] = []

    job["items"].extend(process_items)

    # Update job in database
    await jobs_collection.update_one(
        {"_id": ObjectId(job_id)},
        {"$set": {"items": job["items"]}}
    )

    # Get updated job
    updated_job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
    return updated_job

@router.get("/status/{job_id}")
async def get_job_status(
    job_id: str,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    jobs_collection = user_tenant_info.async_db.jobs

    # Verify job exists
    if not ObjectId.is_valid(job_id):
        raise HTTPException(status_code=400, detail="Invalid job ID")

    job = await jobs_collection.find_one({"_id": ObjectId(job_id)})

    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Here you would typically check the status of the job
    # For this example, we'll just return the job details
    return JSONResponse(
        content={
            "job_id": str(job["_id"]),
            "status": job["status"],
        }
    )

@router.get("/output/{job_id}")
async def get_job_output(
    job_id: str,
    include_presigned_urls: bool = False,
    expiry: int = 3600,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    jobs_collection = user_tenant_info.async_db.jobs
    media_collection = user_tenant_info.async_db.media

    job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    outputs = await media_collection.find({
        "job_id": ObjectId(job_id),
        "metadata.type": "output"
    }).to_list(length=None)

    result = {
        "job_id": str(job_id),
        "status": job["status"],
        "outputs": []
    }

    for output in outputs:
        # Read output data from MinIO
        try:
            response = user_tenant_info.minio_client.get_object(
                bucket_name=output["bucket_name"],
                object_name=output["object_name"]
            )
            output_content = json.loads(response.read().decode('utf-8'))
        except Exception as e:
            logger.error(f"Failed to read output from MinIO: {str(e)}")
            continue

        output_data = {
            "output_id": str(output["_id"]),
            "source_media_id": str(output["metadata"]["source_media_id"]),
            "object_name": output["object_name"],
            "process_type": output["metadata"]["process_type"],
            "result": output_content
        }

        if include_presigned_urls:
            # Generate URL for output JSON
            output_url = user_tenant_info.minio_client.presigned_get_object(
                bucket_name=output["bucket_name"],
                object_name=output["object_name"],
                expires=timedelta(seconds=expiry)
            )
            output_data["presigned_url"] = output_url

            # Generate URLs for diagrams if they exist
            if "diagrams" in output_content:
                for diagram in output_content["diagrams"]:
                    if "image_obj_name" in diagram:
                        diagram_url = user_tenant_info.minio_client.presigned_get_object(
                            bucket_name=output["bucket_name"],
                            object_name=diagram["image_obj_name"],
                            expires=timedelta(seconds=expiry)
                        )
                        diagram["presigned_url"] = diagram_url

        result["outputs"].append(output_data)

    return JSONResponse(content=result)


from app.v1.processes.extract_data_from_image import ExtractQuestions
from app.v1.processes.audio_analysis import AudioAnalysis
from app.v1.processes import available_processes
import json

@router.post("/execute/{job_id}")
async def execute_job(
    job_id: str,
    background_tasks: BackgroundTasks,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    jobs_collection = user_tenant_info.async_db.jobs

    # Verify job exists
    if not ObjectId.is_valid(job_id):
        raise HTTPException(status_code=400, detail="Invalid job ID")

    job = await jobs_collection.find_one({"_id": ObjectId(job_id)})

    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    process_name = job.get("process_name")
    if process_name not in available_processes:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported process: {process_name}"
        )

    # Update job status to in-JobStatus.INPROGRESS
    await jobs_collection.update_one(
        {"_id": ObjectId(job_id)},
        {"$set": {"status": JobStatus.INPROGRESS, "updated_at": datetime.now(timezone.utc)}}
    )

    async def process_job(job_id: str, user_tenant_info: UserTenantDB):
        try:
            # Select the appropriate processor based on process_name
            if process_name == "extract-image-data":
                processor = ExtractQuestions(user_tenant_info)
            elif process_name == "audio-transcribe-analysis":
                processor = AudioAnalysis(user_tenant_info)
            elif process_name == "generic-entity-extraction":
                processor = GenericEntity(user_tenant_info)
            else:
                # Default to ExtractQuestions for backward compatibility
                processor = ExtractQuestions(user_tenant_info)

            await processor.extract_from_job(job_id)

            # Check if project has webhook_callback URL before attempting to send notification
            projects_collection = user_tenant_info.async_db.projects
            project = await projects_collection.find_one({"_id": job["project_id"]})
            if project and project.get("webhook_callback"):
                # After successful processing, send webhook notification
                await send_webhook_notification(job_id, JobStatus.COMPLETED, user_tenant_info, background_tasks)

        except Exception as e:
            logger.error(f"Error processing job {job_id}: {str(e)}")
            await jobs_collection.update_one(
                {"_id": ObjectId(job_id)},
                {"$set": {"status": JobStatus.FAILED, "error": str(e)}}
            )

            # Check if project has webhook_callback URL before attempting to send notification
            projects_collection = user_tenant_info.async_db.projects
            project = await projects_collection.find_one({"_id": job["project_id"]})
            if project and project.get("webhook_callback"):
                # Send webhook notification for failed job
                await send_webhook_notification(job_id, JobStatus.FAILED, user_tenant_info, background_tasks, error=str(e))

    print(f"Job {job_id} is being processed in the background.")
    # Add job to background tasks
    background_tasks.add_task(process_job, job_id, user_tenant_info)

    return JSONResponse(
        content={
            "message": "Job execution started",
            "job_id": str(job_id),
            "status": JobStatus.INPROGRESS
        }
    )


from datetime import timedelta  # Add this import at the top

async def send_webhook_notification(job_id: str, status: JobStatus, user_tenant_info: UserTenantDB, background_tasks: BackgroundTasks, error: str = None):
    """
    Send webhook notification for job status updates.
    This function assumes that the project has a webhook_callback URL configured.
    The caller should check this before calling this function.

    Args:
        job_id: The ID of the job
        status: The current status of the job
        user_tenant_info: User tenant information
        background_tasks: FastAPI background tasks
        error: Optional error message if the job failed
    """
    try:
        # Get job details
        jobs_collection = user_tenant_info.async_db.jobs
        job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
        if not job:
            logger.error(f"Job {job_id} not found when sending webhook notification")
            return

        # Get project details to get webhook_callback URL
        projects_collection = user_tenant_info.async_db.projects
        project = await projects_collection.find_one({"_id": job["project_id"]})
        webhook_url = project["webhook_callback"]

        # Check if a webhook has already been sent for this job with this status
        webhooks_collection = user_tenant_info.async_db.webhooks
        existing_webhook = await webhooks_collection.find_one({
            "job_id": job_id,
            "payload.status": str(status)
        })

        if existing_webhook:
            logger.info(f"Webhook already sent for job {job_id} with status {status}, skipping")
            return

        logger.info(f"Sending webhook notification to {webhook_url} for job {job_id} with status {status}")

        # Get job output URLs if job is completed
        output_urls = []
        if status == JobStatus.COMPLETED:
            media_collection = user_tenant_info.async_db.media
            outputs = await media_collection.find({
                "job_id": ObjectId(job_id),
                "metadata.type": "output"
            }).to_list(length=None)

            for output in outputs:
                try:
                    # Generate presigned URL for output
                    presigned_url = user_tenant_info.minio_client.presigned_get_object(
                        bucket_name=output["bucket_name"],
                        object_name=output["object_name"],
                        expires=timedelta(seconds=3600)  # 1 hour expiry
                    )
                    output_urls.append({
                        "output_id": str(output["_id"]),
                        "presigned_url": presigned_url
                    })
                except Exception as e:
                    logger.error(f"Error generating presigned URL for output {output['_id']}: {str(e)}")

        # Prepare webhook payload
        payload = {
            "job_id": job_id,
            "project_id": str(job["project_id"]),
            "status": str(status),
            "outputs": output_urls
        }

        # Add error message if job failed
        if error:
            payload["error"] = error

        # Create webhook request
        webhook_data = WebhookCreate(
            url=webhook_url,
            job_id=job_id,
            payload=payload
        )

        # Trigger webhook
        await trigger_webhook(webhook_data, background_tasks, user_tenant_info)

    except Exception as e:
        logger.error(f"Error sending webhook notification for job {job_id}: {str(e)}")

@router.get("/media/presigned-url")
async def get_presigned_url(
    object_name: str,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"])),
    expiry: int = 3600  # URL expiry time in seconds, default 1 hour
):
    try:
        # Generate presigned URL with timedelta
        url = user_tenant_info.minio_client.presigned_get_object(
            bucket_name=user_tenant_info.minio_bucket_name,
            object_name=object_name,
            expires=timedelta(seconds=expiry)
        )
        return {"presigned_url": url}
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate presigned URL: {str(e)}"
        )


@router.get("/statuses")
async def get_job_statuses(
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin", "supervisor"]))
):
    """
    Get all available job statuses with their names and labels.

    Returns a list of job statuses in the format:
    [
        { "name": "pending", "label": "Pending" },
        { "name": "in-progress", "label": "In Progress" },
        ...
    ]
    """
    # user_tenant_info is used for authentication/authorization only
    # Define status labels mapping
    status_labels = {
        JobStatus.PENDING: "Pending",
        JobStatus.INPROGRESS: "In Progress",
        JobStatus.COMPLETED: "Completed",
        JobStatus.FAILED: "Failed",
        JobStatus.TIMEOUT: "Timeout",
        JobStatus.CANCELLED: "Cancelled",
        JobStatus.PARTIALLYCOMPLETED: "Partially Completed"
    }

    # Build response list
    statuses = []
    for status_enum, label in status_labels.items():
        statuses.append({
            "name": status_enum.value,
            "label": label
        })

    return statuses


@router.get("/assignment")
async def list_jobs_with_assignments(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search by job name or project name"),
    status: Optional[str] = Query(None, description="Filter by job status"),
    assigned_to: Optional[str] = Query(None, description="Filter by assigned user ID"),
    unassigned_only: bool = Query(False, description="Show only unassigned jobs"),
    project_id: Optional[str] = Query(None, description="Filter by project ID"),
    created_by: Optional[str] = Query(None, description="Filter by user who created the job"),
    created_at_start: Optional[datetime] = Query(None, description="Filter jobs created after this datetime"),
    created_at_end: Optional[datetime] = Query(None, description="Filter jobs created before this datetime"),
    sort_by_created_at: bool = Query(True, description="Sort by created_at field"),
    sort_ascending: bool = Query(False, description="Sort in ascending order (True) or descending order (False)"),
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin", "supervisor"]))
) -> PaginationResponse[JobWithAssignmentInfo]:
    """
    Retrieve a list of all jobs with assigned user ID and name (if assigned),
    including date, assignee, job status, project name, process name, and support for search keywords, sorting, and pagination.
    Also supports filtering for unassigned jobs.

    Parameters:
    - page: Page number (starts at 1)
    - limit: Number of items per page
    - search: Search by job name or project name
    - status: Filter by job status
    - assigned_to: Filter by assigned user ID
    - unassigned_only: Show only unassigned jobs
    - project_id: Filter jobs by project ID
    - created_by: Filter by user who created the job
    - created_at_start: Filter jobs created after this datetime
    - created_at_end: Filter jobs created before this datetime
    - sort_by_created_at: Sort by created_at field (default: True)
    - sort_ascending: Sort in ascending order (True) or descending order (False, default)
    """
    jobs_collection = user_tenant_info.async_db.jobs

    try:
        # logger.debug(f"Assignment endpoint called with params: page={page}, limit={limit}, search={search}, status={status}, assigned_to={assigned_to}, unassigned_only={unassigned_only}")

        # Build aggregation pipeline
        pipeline = []

        # Match stage for basic filtering
        match_conditions = {}

        # Filter by status if provided
        if status:
            match_conditions["status"] = status

        # Filter by project_id if provided
        if project_id:
            if ObjectId.is_valid(project_id):
                match_conditions["project_id"] = ObjectId(project_id)
            else:
                raise HTTPException(status_code=400, detail="Invalid project_id")

        # Filter by created_by if provided
        if created_by:
            if ObjectId.is_valid(created_by):
                match_conditions["created_by"] = ObjectId(created_by)
            else:
                raise HTTPException(status_code=400, detail="Invalid created_by user ID")

        # Filter by created_at date range
        if created_at_start or created_at_end:
            match_conditions["created_at"] = {}
            if created_at_start:
                match_conditions["created_at"]["$gte"] = created_at_start
            if created_at_end:
                match_conditions["created_at"]["$lte"] = created_at_end

        # Filter for unassigned jobs only
        if unassigned_only:
            match_conditions["assigned_to"] = {"$exists": False}
        # Filter by assigned user if provided (only if not filtering for unassigned)
        elif assigned_to:
            if ObjectId.is_valid(assigned_to):
                match_conditions["assigned_to"] = ObjectId(assigned_to)
            else:
                raise HTTPException(status_code=400, detail="Invalid assigned_to user ID")

        if match_conditions:
            pipeline.append({"$match": match_conditions})

        # Lookup stage to join with users collection for assignee information
        pipeline.append({
            "$lookup": {
                "from": "users",
                "localField": "assigned_to",
                "foreignField": "_id",
                "as": "assigned_user"
            }
        })

        # Lookup stage to join with projects collection for project information
        pipeline.append({
            "$lookup": {
                "from": "projects",
                "localField": "project_id",
                "foreignField": "_id",
                "as": "project"
            }
        })

        # Unwind the project array (should be single element)
        pipeline.append({
            "$unwind": {
                "path": "$project",
                "preserveNullAndEmptyArrays": True
            }
        })

        # Search stage if search term is provided
        if search:
            search_conditions = {
                "$or": [
                    {"name": {"$regex": search, "$options": "i"}},
                    {"project.name": {"$regex": search, "$options": "i"}}
                ]
            }
            pipeline.append({"$match": search_conditions})

        # Count total documents for pagination
        count_pipeline = pipeline.copy()
        count_pipeline.append({"$count": "total"})

        count_cursor = await jobs_collection.aggregate(count_pipeline)
        count_result = await count_cursor.to_list(length=1)
        total = count_result[0]["total"] if count_result else 0

        # Add sorting by created_at
        if sort_by_created_at:
            sort_order = 1 if sort_ascending else -1  # 1 for ascending, -1 for descending
            pipeline.append({"$sort": {"created_at": sort_order}})
        else:
            # Default sorting by created_at descending if no sorting specified
            pipeline.append({"$sort": {"created_at": -1}})

        # Add pagination stages
        skip = (page - 1) * limit
        pipeline.append({"$skip": skip})
        pipeline.append({"$limit": limit})

        # Project stage to select and format fields
        pipeline.append({
            "$project": {
                "_id": 1,
                "name": 1,
                "description": 1,
                "status": 1,
                "created_at": 1,
                "assigned_to": 1,
                "project_id": 1,  # Include the project_id field
                "process_name": 1,  # Include the process_name field
                "assigned_to_name": {
                    "$ifNull": [{"$arrayElemAt": ["$assigned_user.username", 0]}, None]
                },
                "project_name": {
                    "$ifNull": ["$project.name", "Unknown Project"]
                }
            }
        })

        # Execute the aggregation
        logger.debug(f"Executing aggregation pipeline: {pipeline}")
        cursor = await jobs_collection.aggregate(pipeline)
        jobs = await cursor.to_list(length=None)
        logger.debug(f"Retrieved {len(jobs)} jobs")

        return {
            "data": jobs,
            "meta": {
                "page": page,
                "limit": limit,
                "total": total,
                "total_pages": (total + limit - 1) // limit if limit > 0 else 0,
            },
        }

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error retrieving jobs with assignments: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve jobs: {str(e)}")


@router.get("/{job_id}", response_model=Job)
async def get_job_details(
    job_id: str,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    """
    Get details of a specific job without the items array.
    Items are provided through a separate endpoint for pagination.
    """
    jobs_collection = user_tenant_info.async_db.jobs

    # Use projection to exclude items field
    job = await jobs_collection.find_one(
        {"_id": ObjectId(job_id)},
        {"items": 0}  # Exclude items field
    )

    job["items"] = []

    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    return job


@router.get("/{job_id}/items")
async def get_job_items(
    job_id: str,
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    status: Optional[str] = None,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
) -> PaginationResponse[ProcessItem]:
    """
    Get items for a specific job with pagination and optional status filtering.
    """
    jobs_collection = user_tenant_info.async_db.jobs

    # First check if job exists
    job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Get items with pagination
    items = job.get("items", [])

    # Filter by status if provided
    if status:
        items = [item for item in items if item.get("status") == status]

    # Calculate pagination
    total = len(items)
    start_idx = (page - 1) * limit
    end_idx = min(start_idx + limit, total)

    # Get paginated items
    paginated_items = items[start_idx:end_idx] if start_idx < total else []

    return {
        "data": paginated_items,
        "meta": {
            "page": page,
            "limit": limit,
            "total": total,
            "total_pages": (total + limit - 1) // limit if limit > 0 else 0,
        },
    }


@router.delete("/{job_id}")
async def delete_job(
    job_id: str,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    """
    Delete a job and all its associated media files.
    """
    jobs_collection = user_tenant_info.async_db.jobs
    media_collection = user_tenant_info.async_db.media

    # Verify job exists
    if not ObjectId.is_valid(job_id):
        raise HTTPException(status_code=400, detail="Invalid job ID")

    job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    try:
        # Delete all media files associated with the job from MinIO
        media_files = await media_collection.find({"job_id": ObjectId(job_id)}).to_list(length=None)

        for media_file in media_files:
            try:
                user_tenant_info.minio_client.remove_object(
                    bucket_name=media_file["bucket_name"],
                    object_name=media_file["object_name"]
                )
            except Exception as e:
                logger.warning(f"Failed to delete media file {media_file['object_name']}: {str(e)}")

        # Delete all media records from database
        await media_collection.delete_many({"job_id": ObjectId(job_id)})

        # Delete the job
        result = await jobs_collection.delete_one({"_id": ObjectId(job_id)})

        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Job not found")

        return {"message": "Job deleted successfully", "job_id": job_id}

    except Exception as e:
        logger.error(f"Error deleting job {job_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete job: {str(e)}")


@router.delete("/{job_id}/media/{media_id}")
async def delete_media_from_job(
    job_id: str,
    media_id: str,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    """
    Delete a specific media file from a job.
    """
    jobs_collection = user_tenant_info.async_db.jobs
    media_collection = user_tenant_info.async_db.media

    # Verify job and media IDs
    if not ObjectId.is_valid(job_id):
        raise HTTPException(status_code=400, detail="Invalid job ID")
    if not ObjectId.is_valid(media_id):
        raise HTTPException(status_code=400, detail="Invalid media ID")

    # Verify job exists
    job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Verify media exists and belongs to the job
    media_file = await media_collection.find_one({
        "_id": ObjectId(media_id),
        "job_id": ObjectId(job_id)
    })
    if not media_file:
        raise HTTPException(status_code=404, detail="Media file not found in this job")

    try:
        # Delete from MinIO
        user_tenant_info.minio_client.remove_object(
            bucket_name=media_file["bucket_name"],
            object_name=media_file["object_name"]
        )

        # Delete from database
        result = await media_collection.delete_one({"_id": ObjectId(media_id)})

        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Media file not found")

        # Remove the media reference from job's items array
        # Find and remove any items that reference this media_id as input_id or output_id
        updated_items = []
        for item in job.get("items", []):
            # Keep items that don't reference the deleted media
            if (str(item.get("input_id")) != media_id and
                (item.get("output_id") is None or str(item.get("output_id")) != media_id)):
                updated_items.append(item)

        # Update job with the filtered items array
        await jobs_collection.update_one(
            {"_id": ObjectId(job_id)},
            {"$set": {"items": updated_items}}
        )

        return {
            "message": "Media file deleted successfully",
            "media_id": media_id,
            "removed_from_job_items": len(job.get("items", [])) - len(updated_items)
        }

    except Exception as e:
        logger.error(f"Error deleting media {media_id} from job {job_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete media file: {str(e)}")


@router.put("/{job_id}", response_model=Job)
async def update_job(
    job_id: str,
    job_update: JobUpdate,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    """
    Update job details (name, description).
    """
    jobs_collection = user_tenant_info.async_db.jobs

    # Verify job exists
    if not ObjectId.is_valid(job_id):
        raise HTTPException(status_code=400, detail="Invalid job ID")

    job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Build update data
    update_data = {}
    if job_update.name is not None:
        update_data["name"] = job_update.name
    if job_update.description is not None:
        update_data["description"] = job_update.description

    try:
        # Update the job
        result = await jobs_collection.update_one(
            {"_id": ObjectId(job_id)},
            {"$set": update_data}
        )

        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Job not found")

        # Return updated job
        updated_job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
        return updated_job

    except Exception as e:
        logger.error(f"Error updating job {job_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update job: {str(e)}")


@router.post("/{job_id}/output/{output_id}/verify")
async def verify_job_output(
    job_id: str,
    output_id: str,
    verification: OutputVerification,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin", "supervisor"]))
):
    """
    Verify a job output.
    """
    jobs_collection = user_tenant_info.async_db.jobs
    media_collection = user_tenant_info.async_db.media

    # Verify job and output IDs
    if not ObjectId.is_valid(job_id):
        raise HTTPException(status_code=400, detail="Invalid job ID")
    if not ObjectId.is_valid(output_id):
        raise HTTPException(status_code=400, detail="Invalid output ID")

    # Verify job exists
    job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Verify output exists and belongs to the job
    output = await media_collection.find_one({
        "_id": ObjectId(output_id),
        "job_id": ObjectId(job_id),
        "metadata.type": "output"
    })
    if not output:
        raise HTTPException(status_code=404, detail="Output not found in this job")

    try:
        # Update output with verification info
        verification_data = {
            "metadata.verification.verified": verification.verified,
            "metadata.verification.verification_notes": verification.verification_notes,
            "metadata.verification.verified_by": ObjectId(user_tenant_info.user.id),
            "metadata.verification.verified_at": datetime.now(timezone.utc)
        }

        result = await media_collection.update_one(
            {"_id": ObjectId(output_id)},
            {"$set": verification_data}
        )

        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Output not found")

        return {"message": "Output verification updated successfully", "output_id": output_id}

    except Exception as e:
        logger.error(f"Error verifying output {output_id} for job {job_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to verify output: {str(e)}")


@router.put("/{job_id}/output/{output_id}/edit")
async def edit_job_output(
    job_id: str,
    output_id: str,
    edit: OutputEdit,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    """
    Edit a job output. Expects JSON data wrapped in {result} structure.
    """
    jobs_collection = user_tenant_info.async_db.jobs
    media_collection = user_tenant_info.async_db.media

    # Verify job and output IDs
    if not ObjectId.is_valid(job_id):
        raise HTTPException(status_code=400, detail="Invalid job ID")
    if not ObjectId.is_valid(output_id):
        raise HTTPException(status_code=400, detail="Invalid output ID")

    # Verify job exists
    job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Verify output exists and belongs to the job
    output = await media_collection.find_one({
        "_id": ObjectId(output_id),
        "job_id": ObjectId(job_id),
        "metadata.type": "output"
    })
    if not output:
        raise HTTPException(status_code=404, detail="Output not found in this job")

    try:
        # Read current output data from MinIO to preserve edit history
        current_output_content = None
        try:
            response = user_tenant_info.minio_client.get_object(
                bucket_name=output["bucket_name"],
                object_name=output["object_name"]
            )
            current_output_content = json.loads(response.read().decode('utf-8'))
        except Exception as e:
            logger.warning(f"Could not read current output content: {str(e)}")
            current_output_content = {}

        # Prepare the updated output data
        updated_output = {
            **edit.result,  # Use the result field from the wrapped structure
            "edit_history": current_output_content.get("edit_history", [])
        }

        # Add edit history entry
        edit_entry = {
            "edited_at": datetime.now(timezone.utc).isoformat(),
            "edited_by": str(user_tenant_info.user.id),
        }
        updated_output["edit_history"].append(edit_entry)

        # Upload updated content to MinIO
        updated_content_bytes = json.dumps(updated_output).encode('utf-8')
        user_tenant_info.minio_client.put_object(
            bucket_name=output["bucket_name"],
            object_name=output["object_name"],
            data=io.BytesIO(updated_content_bytes),
            length=len(updated_content_bytes),
            content_type="application/json"
        )

        # Update metadata in database
        edit_metadata = {
            "metadata.last_edited_at": datetime.now(timezone.utc),
            "metadata.last_edited_by": ObjectId(user_tenant_info.user.id),
            "metadata.edit_count": len(updated_output["edit_history"])
        }

        result = await media_collection.update_one(
            {"_id": ObjectId(output_id)},
            {"$set": edit_metadata}
        )

        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Output not found")

        return {
            "message": "Output edited successfully",
            "output_id": output_id,
            "edit_count": len(updated_output["edit_history"]),
            "last_edited_at": edit_metadata["metadata.last_edited_at"].isoformat()
        }

    except Exception as e:
        logger.error(f"Error editing output {output_id} for job {job_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to edit output: {str(e)}")

@router.post("/assign")
async def assign_jobs_to_user(
    assignment: JobAssignment,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin", "supervisor"]))
):
    """
    Assign one or more jobs to a user.
    """
    jobs_collection = user_tenant_info.async_db.jobs
    users_collection = user_tenant_info.async_db.users

    # Verify user exists
    user = await users_collection.find_one({"_id": ObjectId(assignment.user_id)})
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Verify all jobs exist
    job_ids = [ObjectId(job_id) for job_id in assignment.job_ids]
    jobs = await jobs_collection.find({"_id": {"$in": job_ids}}).to_list(length=None)

    if len(jobs) != len(assignment.job_ids):
        found_ids = {str(job["_id"]) for job in jobs}
        missing_ids = set(assignment.job_ids) - found_ids
        raise HTTPException(
            status_code=404,
            detail=f"Jobs not found: {', '.join(missing_ids)}"
        )

    try:
        # Update all jobs with assignment information
        assignment_data = {
            "assigned_to": ObjectId(assignment.user_id),
            "assigned_at": datetime.now(timezone.utc),
            "assigned_by": ObjectId(user_tenant_info.user.id)
        }

        result = await jobs_collection.update_many(
            {"_id": {"$in": job_ids}},
            {"$set": assignment_data}
        )

        # Collect project information for the assigned jobs
        projects_info = {}
        for job in jobs:
            project_id = str(job["project_id"])
            if project_id not in projects_info:
                projects_info[project_id] = {
                    "project_id": project_id,
                    "job_ids": []
                }
            projects_info[project_id]["job_ids"].append(str(job["_id"]))

        # Get project names for better response
        projects_collection = user_tenant_info.async_db.projects
        project_ids = [ObjectId(pid) for pid in projects_info.keys()]
        projects = await projects_collection.find(
            {"_id": {"$in": project_ids}},
            {"_id": 1, "name": 1}
        ).to_list(length=None)

        # Add project names to the response
        for project in projects:
            project_id = str(project["_id"])
            if project_id in projects_info:
                projects_info[project_id]["project_name"] = project["name"]

        return {
            "message": f"Successfully assigned {result.modified_count} jobs to user {user['username']}",
            "assigned_jobs": assignment.job_ids,
            "assigned_to": assignment.user_id,
            "assigned_to_username": user["username"],
            "projects": list(projects_info.values())
        }

    except Exception as e:
        logger.error(f"Error assigning jobs {assignment.job_ids} to user {assignment.user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to assign jobs: {str(e)}")


@router.post("/unassign")
async def unassign_jobs(
    unassignment: JobUnassignment,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin", "supervisor"]))
):
    """
    Unassign one or more jobs from their current assignees.
    """
    jobs_collection = user_tenant_info.async_db.jobs

    # Verify all jobs exist
    job_ids = [ObjectId(job_id) for job_id in unassignment.job_ids]
    jobs = await jobs_collection.find({"_id": {"$in": job_ids}}).to_list(length=None)

    if len(jobs) != len(unassignment.job_ids):
        found_ids = {str(job["_id"]) for job in jobs}
        missing_ids = set(unassignment.job_ids) - found_ids
        raise HTTPException(
            status_code=404,
            detail=f"Jobs not found: {', '.join(missing_ids)}"
        )

    try:
        # Remove assignment information from all jobs
        result = await jobs_collection.update_many(
            {"_id": {"$in": job_ids}},
            {
                "$unset": {
                    "assigned_to": "",
                    "assigned_at": "",
                    "assigned_by": ""
                }
            }
        )

        # Collect project information for the unassigned jobs
        projects_info = {}
        for job in jobs:
            project_id = str(job["project_id"])
            if project_id not in projects_info:
                projects_info[project_id] = {
                    "project_id": project_id,
                    "job_ids": []
                }
            projects_info[project_id]["job_ids"].append(str(job["_id"]))

        # Get project names for better response
        projects_collection = user_tenant_info.async_db.projects
        project_ids = [ObjectId(pid) for pid in projects_info.keys()]
        projects = await projects_collection.find(
            {"_id": {"$in": project_ids}},
            {"_id": 1, "name": 1}
        ).to_list(length=None)

        # Add project names to the response
        for project in projects:
            project_id = str(project["_id"])
            if project_id in projects_info:
                projects_info[project_id]["project_name"] = project["name"]

        return {
            "message": f"Successfully unassigned {result.modified_count} jobs",
            "unassigned_jobs": unassignment.job_ids,
            "projects": list(projects_info.values())
        }

    except Exception as e:
        logger.error(f"Error unassigning jobs {unassignment.job_ids}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to unassign jobs: {str(e)}")


@router.get("/{job_id}/verification-count", response_model=VerificationCount)
async def get_job_verification_count(
    job_id: str,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin", "supervisor"]))
):
    """
    Get the count of verified and unverified outputs for a job.

    For a given job, returns the total count of input media that have verified and unverified outputs.
    The sum of verified and unverified outputs equals the total number of input media for the job.

    If the input media is not processed yet, it counts as unverified.
    """
    jobs_collection = user_tenant_info.async_db.jobs
    media_collection = user_tenant_info.async_db.media

    # Verify job exists
    if not ObjectId.is_valid(job_id):
        raise HTTPException(status_code=400, detail="Invalid job ID")

    job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    try:
        # Get total number of input media from job items
        total_input_media = len(job.get("items", []))

        # Count verified outputs
        verified_count = await media_collection.count_documents({
            "job_id": ObjectId(job_id),
            "metadata.type": "output",
            "metadata.verification.verified": True
        })

        # Calculate unverified count (total input media - verified outputs)
        unverified_count = total_input_media - verified_count

        return VerificationCount(
            verified=verified_count,
            unverified=unverified_count
        )

    except Exception as e:
        logger.error(f"Error getting verification count for job {job_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get verification count: {str(e)}")
